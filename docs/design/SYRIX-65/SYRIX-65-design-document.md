# SYRIX-65: Third-Party Storage Providers Prevention Design Document

**Policy ID:** MS.EXO.8.5v1  
**JIRA:** SYRIX-65  
**CIS Reference:** 6.5.3  
**Created:** 2025-01-14  
**Status:** Implemented

## **1. Overview**

Implementing Exchange Online third-party storage provider restrictions to prevent data leakage through unauthorized external storage integrations in Outlook on the web. This security enhancement fits logically in Section 8 "Data Loss Prevention Solutions" as MS.EXO.8.5v1.

## **2. Technical Architecture**

### **2.1 Component Structure (4-Component Bundle)**

1. **@PolicyRemediator Class**: `OWAThirdPartyStorageRemediator.java`
2. **Configuration Method**: `getOwaThirdPartyStorageConfig()` in `ExchangeOnlineConfigurationService.java`
3. **Rego Policy Rule**: MS.EXO.8.5v1 validation logic in `EXOConfig.rego`
4. **CISA Baseline Documentation**: Section 8.5 third-party storage configuration

### **2.2 Class Design**

```java
@PolicyRemediator("MS.EXO.8.5v1")
public class OWAThirdPartyStorageRemediator extends ExchangeBaseRemediator implements IPolicyRemediatorRollback {
    
    public OWAThirdPartyStorageRemediator(MicrosoftGraphClient graphClient,
                                               PowerShellClient exchangeClient,
                                               ObjectNode configNode,
                                               ExchangeRemediationContext exchangeRemediationContext,
                                               ExchangeRemediationConfig remediationConfig) {
        super(graphClient, exchangeClient, configNode, exchangeRemediationContext, remediationConfig);
    }

    @Override
    public CompletableFuture<JsonNode> remediate()
    
    @Override
    public CompletableFuture<PolicyChangeResult> remediate_()
    
    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult)
    
    private List<OwaMailboxPolicy> getOwaMailboxPolicies()
    private boolean hasThirdPartyStorageEnabled(OwaMailboxPolicy policy)
    private CompletableFuture<PolicyChangeResult> updateOwaMailboxPolicy(OwaMailboxPolicy policy)
}
```

### **2.3 PowerShell Integration**

**Commands Required:**
- `Get-OwaMailboxPolicy` - Retrieve current third-party storage settings
- `Set-OwaMailboxPolicy -AdditionalStorageProvidersAvailable $false` - Disable third-party storage

**Parameters Targeted:**
- `AdditionalStorageProvidersAvailable`: Controls access to third-party storage providers
- `AllowedFileTypes`: Restricts file types when third-party storage is needed
- `AllowedMimeTypes`: Restricts MIME types for enhanced security

### **2.4 Configuration Service Integration**

```java
public CompletableFuture<JsonNode> getOwaThirdPartyStorageConfig() {
    PowerShellClient.CommandRequest owaPolicyRequest = 
        new PowerShellClient.CommandRequest(ExoConstants.GET_OWA_MAILBOX_POLICY, Map.of());
    
    return withRetry(() -> this.powerShellClient.executeCmdletCommand(owaPolicyRequest), 
                   owaPolicyRequest.getCmdletName())
        .thenApply(this::filterOwaThirdPartyStorageSettings);
}
```

## **3. Data Flow Architecture**

### **3.1 Configuration Retrieval Flow**
```
ExchangeOnlineConfigurationService → Get-OwaMailboxPolicy → Filter Relevant Fields → Store in configNode
```

### **3.2 Remediation Flow**
```
OWAThirdPartyStorageRemediator → Identify Non-Compliant Policies → Set-OwaMailboxPolicy → Verify Changes → Track Results
```

### **3.3 Rego Validation Flow**
```
ThirdPartyStorageConfig := input.owa_third_party_storage_config
ThirdPartyStorageEnabled := [Policy | Policy := ThirdPartyStorageConfig[_]; Policy.AdditionalStorageProvidersAvailable == true]
RequirementMet := count(ThirdPartyStorageEnabled) == 0
```

## **4. Security Implementation**

### **4.1 Data Loss Prevention Strategy**

**Threat Vectors Addressed:**
- Unauthorized data exfiltration through Dropbox integration
- OneDrive Consumer account data leakage
- Google Drive unauthorized file sharing
- Other third-party cloud storage bypass

**Security Controls:**
- Complete blocking of external storage provider APIs
- Prevention of file upload/download through unauthorized channels
- Enforcement of organizational storage boundaries
- Audit trail of policy changes through parameter tracking

### **4.2 MITRE ATT&CK Mapping**

- **T1567.002** (Exfiltration to Cloud Storage): Direct mitigation
- **T1048** (Exfiltration Over Alternative Protocol): Prevents alternative channels
- **T1530** (Data from Cloud Storage): Blocks unauthorized storage access
- **T1074.002** (Remote Data Staging): Prevents external staging areas

## **5. Rollback Implementation**

### **5.1 Rollback Strategy**
```java
@Override
public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
    // Extract original settings from ParameterChangeResult
    // Restore AdditionalStorageProvidersAvailable to previous state
    // Verify rollback success
    // Track rollback parameter changes
}
```

### **5.2 Parameter Change Tracking**
```java
ParameterChangeResult paramChange = new ParameterChangeResult()
    .timeStamp(Instant.now())
    .parameter(policy.identity)
    .prevValue(previousValue)
    .newValue(newValue)
    .status(ParameterChangeStatus.SUCCESS);
```

## **6. Testing Strategy**

### **6.1 Unit Test Coverage**
- Policy retrieval and filtering logic
- Remediation decision logic (hasThirdPartyStorageEnabled)
- PowerShell command execution mocking
- Rollback functionality verification
- Error handling and exception scenarios

### **6.2 Integration Test Scenarios**
- End-to-end remediation flow
- Multiple OWA policies with different configurations
- Network failure and retry scenarios
- Rego policy validation with various input combinations

## **7. Compliance and Documentation**

### **7.1 CIS MS365 Benchmark Alignment**
- **Control 6.5.3**: Prevent third-party storage provider usage
- **Criticality**: SHALL (mandatory implementation)
- **Scope**: Outlook on the web interface restrictions

### **7.2 CISA Baseline Integration**
```markdown
#### MS.EXO.8.5v1
Third-party storage providers SHALL be blocked in Outlook on the web to prevent data leakage through unauthorized external storage integrations.

- Rationale: Prevents data exfiltration through unauthorized channels
- Implementation: Set-OwaMailboxPolicy -AdditionalStorageProvidersAvailable $false  
- MITRE ATT&CK: T1567.002, T1048, T1530, T1074.002
```

## **8. Performance Considerations**

### **8.1 Scalability**
- Efficient batch processing of multiple OWA policies
- Parallel execution of policy updates using CompletableFuture
- Minimal PowerShell command overhead

### **8.2 Error Resilience**
- Retry mechanism for transient PowerShell failures
- Graceful handling of missing or malformed policy data
- Comprehensive logging for troubleshooting

## **9. Implementation Checklist**

- [x] @PolicyRemediator class with MS.EXO.8.5v1 annotation (OWAThirdPartyStorageRemediator.java)
- [x] Configuration service method integration
- [x] Rego policy validation rule
- [x] CISA baseline documentation
- [x] ExoConstants updates for new commands
- [x] Unit tests for remediation logic (OWAThirdPartyStorageRemediatorTest.java)
- [ ] Integration tests for end-to-end flow
- [ ] Performance testing with multiple policies

## **10. Deployment Considerations**

### **10.1 Prerequisites**
- Exchange Online PowerShell module permissions
- OWA mailbox policy read/write access
- Organizational admin privileges for policy modifications

### **10.2 Rollout Strategy**
- Test environment validation first
- Phased deployment across organizational units
- Monitor for user impact on legitimate storage workflows
- Document any required user training or policy exceptions

## **11. Monitoring and Alerting**

### **11.1 Success Metrics**
- Number of policies successfully remediated
- Zero third-party storage providers enabled post-remediation
- Successful rego validation results

### **11.2 Alert Conditions**
- Policy remediation failures
- Unexpected third-party storage re-enablement
- PowerShell command execution failures
- Rego validation requirement not met

---

**Document Version:** 1.0  
**Last Updated:** 2025-01-14  
**Next Review:** 2025-04-14