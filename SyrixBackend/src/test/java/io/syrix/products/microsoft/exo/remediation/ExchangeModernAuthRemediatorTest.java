package io.syrix.products.microsoft.exo.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.exo.ExoConstants;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for the ExchangeModernAuthRemediator class.

 * Tests cover the complete remediation lifecycle including:
 * - Detection of current Modern Authentication status
 * - Remediation when Modern Auth is disabled
 * - Requirement already met scenarios
 * - Error handling for configuration issues
 * - Rollback functionality for policy reversals
 */
@ExtendWith(MockitoExtension.class)
class ExchangeModernAuthRemediatorTest {

	@Mock
	private MicrosoftGraphClient graphClient;

	@Mock
	private PowerShellClient exchangeClient;

	@Mock
	private ExchangeRemediationContext remediationContext;

	@Mock
	private ExchangeRemediationConfig remediationConfig;

	@Captor
	private ArgumentCaptor<PowerShellClient.CommandRequest> requestCaptor;

	private ExchangeModernAuthRemediator remediator;
	private ObjectMapper objectMapper;
	private ObjectNode configNode;

	@BeforeEach
	void setUp() {
		objectMapper = new ObjectMapper();
		configNode = objectMapper.createObjectNode();

		// Create mock organization config with OAuth2ClientProfileEnabled = false
		ObjectNode orgConfig = objectMapper.createObjectNode();
		orgConfig.put(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED, false);
		configNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(orgConfig));

		remediator = new ExchangeModernAuthRemediator(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);
	}

	@Nested
	@DisplayName("Modern Authentication Status Detection Tests")
	class ModernAuthStatusTests {

		@Test
		@DisplayName("Should detect that Modern Authentication is already enabled")
		void shouldDetectModernAuthAlreadyEnabled() {
			// Given - Modern Auth is already enabled
			ObjectNode enabledOrgConfig = objectMapper.createObjectNode();
			enabledOrgConfig.put(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED, true);
			configNode.set(ExoConstants.CONFIG_KEY_ORGANIZATION, objectMapper.createArrayNode().add(enabledOrgConfig));

			remediator = new ExchangeModernAuthRemediator(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);

			// When
			PolicyChangeResult result = remediator.remediate_().join();

			// Then
			assertThat(result.getResult()).isEqualTo(RemediationResult.REQUIREMENT_MET);
			assertThat(result.getPolicyId()).isEqualTo("MS.EXO.5.2v1");

			// Verify no PowerShell commands were executed since requirement is already met
			verify(exchangeClient, never()).executeCmdletCommand(any(PowerShellClient.CommandRequest.class));
		}

		@Test
		@DisplayName("Should detect that Modern Authentication needs to be enabled")
		void shouldDetectModernAuthNeedsEnabling() {
			// Given - Modern Auth is disabled (configNode already set up in setUp())
			ObjectNode mockResponse = objectMapper.createObjectNode();
			mockResponse.put("status", "success");
			when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
					.thenReturn(CompletableFuture.completedFuture(mockResponse));

			// When
			PolicyChangeResult result = remediator.remediate_().join();

			// Then
			assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
			assertThat(result.getPolicyId()).isEqualTo("MS.EXO.5.2v1");
			assertThat(result.getDesc()).contains("Successfully enabled Modern Authentication");

			// Verify correct PowerShell command was executed
			verify(exchangeClient, times(1)).executeCmdletCommand(requestCaptor.capture());
			PowerShellClient.CommandRequest request = requestCaptor.getValue();
			assertThat(request.getCmdletName()).isEqualTo(ExoConstants.SET_ORGANIZATION_CONFIG);

			Map<String, Object> parameters = new HashMap<>();
			parameters.put(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED, true);
			parameters.put(ExoConstants.ERROR_ACTION, ExoConstants.ERROR_ACTION_STOP);

			Map<String, Object> cmdletInput = new HashMap<>();
			cmdletInput.put("CmdletName", ExoConstants.SET_ORGANIZATION_CONFIG);
			cmdletInput.put("Parameters", parameters);

			Map<String, Object> expectedBody = new HashMap<>();
			expectedBody.put("CmdletInput", cmdletInput);

			assertThat(request.toRequestBody()).isEqualTo(expectedBody);
		}

		@Test
		@DisplayName("Should handle missing organization configuration")
		void shouldHandleMissingOrganizationConfig() {
			// Given - No organization config available
			configNode = objectMapper.createObjectNode();
			remediator = new ExchangeModernAuthRemediator(graphClient, exchangeClient, configNode, remediationContext, remediationConfig);

			// When
			PolicyChangeResult result = remediator.remediate_().join();

			// Then
			assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
			assertThat(result.getPolicyId()).isEqualTo("MS.EXO.5.2v1");
			assertThat(result.getDesc()).isEqualTo(ExoConstants.ERROR_NO_ORGANIZATION_CONFIG);

			// Verify no PowerShell commands were executed
			verify(exchangeClient, never()).executeCmdletCommand(any(PowerShellClient.CommandRequest.class));
		}
	}

	@Nested
	@DisplayName("Modern Authentication Remediation Tests")
	class RemediationTests {

		@Test
		@DisplayName("Should successfully enable Modern Authentication")
		void shouldSuccessfullyEnableModernAuth() {
			// Given
			JsonNode successResponse = objectMapper.createObjectNode();
			when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
					.thenReturn(CompletableFuture.completedFuture(successResponse));

			// When
			PolicyChangeResult result = remediator.remediate_().join();

			// Then
			assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
			assertThat(result.getDesc()).contains("Successfully enabled Modern Authentication");

			List<ParameterChangeResult> changes = result.getChanges();
			assertThat(changes).hasSize(1);

			ParameterChangeResult change = changes.get(0);
			assertThat(change.getParameter()).isEqualTo(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED);
			assertThat(change.getPrevValue()).isEqualTo(false);
			assertThat(change.getNewValue()).isEqualTo(true);
			assertThat(change.getStatus()).isEqualTo(ParameterChangeStatus.SUCCESS);
		}

		@Test
		@DisplayName("Should handle PowerShell command failure")
		void shouldHandlePowerShellCommandFailure() {
			// Given
			RuntimeException powerShellError = new RuntimeException("PowerShell execution failed");
			when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
					.thenReturn(CompletableFuture.failedFuture(powerShellError));

			// When
			PolicyChangeResult result = remediator.remediate_().join();

			// Then
			assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
			assertThat(result.getDesc()).contains("Failed to enable Modern Authentication");
			assertThat(result.getDesc()).contains("PowerShell execution failed");

			List<ParameterChangeResult> changes = result.getChanges();
			assertThat(changes).hasSize(1);
			assertThat(changes.get(0).getStatus()).isEqualTo(ParameterChangeStatus.FAILED);
		}
	}

	@Nested
	@DisplayName("Rollback Functionality Tests")
	class RollbackTests {

		@Test
		@DisplayName("Should successfully rollback Modern Authentication changes")
		void shouldSuccessfullyRollbackChanges() {
			// Given - Create a successful fix result to rollback
			ParameterChangeResult originalChange = new ParameterChangeResult()
					.parameter(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED)
					.prevValue(false)
					.newValue(true)
					.status(ParameterChangeStatus.SUCCESS);

			PolicyChangeResult fixResult = new PolicyChangeResult();
			fixResult.changes(List.of(originalChange));

			JsonNode rollbackResponse = objectMapper.createObjectNode();
			when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
					.thenReturn(CompletableFuture.completedFuture(rollbackResponse));

			// When
			PolicyChangeResult rollbackResult = remediator.rollback(fixResult).join();

			// Then
			assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.SUCCESS);
			assertThat(rollbackResult.getDesc()).contains("Successfully disabled Modern Authentication");

			// Verify correct rollback command was executed
			verify(exchangeClient, times(1)).executeCmdletCommand(requestCaptor.capture());
			PowerShellClient.CommandRequest request = requestCaptor.getValue();
			assertThat(request.getCmdletName()).isEqualTo(ExoConstants.SET_ORGANIZATION_CONFIG);

			Map<String, Object> requestBody = request.toRequestBody();
			assertThat(requestBody).isNotNull();
			Map<String, Object> cmdletInput = (Map<String, Object>) requestBody.get("CmdletInput");
			assertThat(cmdletInput).isNotNull();
			Map<String, Object> parameters = (Map<String, Object>) cmdletInput.get("Parameters");
			assertThat(parameters).isNotNull();
			assertThat(parameters).containsEntry(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED, false); // Rolling back to previous value
		}

		@Test
		@DisplayName("Should handle rollback when original change failed")
		void shouldHandleRollbackWhenOriginalChangeFailed() {
			// Given - Create a failed fix result
			ParameterChangeResult failedChange = new ParameterChangeResult()
					.parameter(ExoConstants.OAUTH2_CLIENT_PROFILE_ENABLED)
					.prevValue(false)
					.newValue(true)
					.status(ParameterChangeStatus.FAILED);

			PolicyChangeResult fixResult = new PolicyChangeResult();
			fixResult.changes(List.of(failedChange));

			// When
			PolicyChangeResult rollbackResult = remediator.rollback(fixResult).join();

			// Then
			assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.FAILED);
			assertThat(rollbackResult.getDesc()).contains("Rollback skipped");

			// Verify no PowerShell commands were executed for failed rollback
			verify(exchangeClient, never()).executeCmdletCommand(any(PowerShellClient.CommandRequest.class));
		}

		@Test
		@DisplayName("Should handle rollback with no changes in fix result")
		void shouldHandleRollbackWithNoChanges() {
			// Given - Empty fix result
			PolicyChangeResult fixResult = new PolicyChangeResult();
			fixResult.changes(List.of());

			// When
			PolicyChangeResult rollbackResult = remediator.rollback(fixResult).join();

			// Then
			assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.FAILED);
			assertThat(rollbackResult.getDesc()).contains("No changes found in fix result");

			// Verify no PowerShell commands were executed
			verify(exchangeClient, never()).executeCmdletCommand(any(PowerShellClient.CommandRequest.class));
		}
	}

	@Nested
	@DisplayName("JSON Response Handling Tests")
	class JsonResponseTests {

		@Test
		@DisplayName("Should handle successful JSON remediate() response")
		void shouldHandleSuccessfulJsonResponse() {
			// Given
			ObjectNode successResponse = objectMapper.createObjectNode();
			successResponse.put("status", "success");
			when(exchangeClient.executeCmdletCommand(any(PowerShellClient.CommandRequest.class)))
					.thenReturn(CompletableFuture.completedFuture(successResponse));

			// When
			JsonNode result = remediator.remediate().join();

			// Then - Verify JSON response structure
			assertThat(result.get("result").asText()).isEqualTo("SUCCESS");
			assertThat(result.get("policyId").asText()).isEqualTo("MS.EXO.5.2v1");
			assertThat(result.get("desc").asText()).contains("Successfully enabled Modern Authentication");
			assertThat(result.has("changes")).isTrue();
		}
	}

	@Nested
	@DisplayName("Constructor Tests")
	class ConstructorTests {

		@Test
		@DisplayName("Should create remediator with rollback constructor")
		void shouldCreateRemediatorWithRollbackConstructor() {
			// When
			ExchangeModernAuthRemediator rollbackRemediator =
					new ExchangeModernAuthRemediator(graphClient, exchangeClient);

			// Then
			assertThat(rollbackRemediator).isNotNull();
			// Verify the rollback constructor sets null values for optional parameters
		}
	}
}